# Include package metadata
include VERSION
include README.md
include LICENSE
include CHANGELOG.md
include *.md

# Include configuration files
include pyproject.toml
include setup.py
include backend/requirements.txt

# Include backend files
recursive-include backend *.py
recursive-include backend *.txt
recursive-include backend *.md
recursive-include backend *.yml
recursive-include backend *.yaml
recursive-include backend *.json
recursive-include backend *.cfg
recursive-include backend *.ini

# Include frontend source files
include frontend/package.json
include frontend/package-lock.json
include frontend/next.config.js
include frontend/next.config.ts
include frontend/tailwind.config.ts
include frontend/postcss.config.mjs
include frontend/tsconfig.json
include frontend/components.json
include frontend/eslint.config.mjs
recursive-include frontend/src *
recursive-include frontend/public *

# Include frontend build files (if built)
recursive-include frontend/dist *
recursive-include frontend/build *
recursive-include frontend/.next *

# Include Node.js service files
include nodejs-service/package.json
include nodejs-service/package-lock.json
recursive-include nodejs-service/src *
recursive-include nodejs-service/config *
recursive-include nodejs-service/tests *

# Include static files
recursive-include backend/app/static *
recursive-include backend/app/templates *

# Include shell scripts
include *.sh

# Include documentation
recursive-include docs *.rst
recursive-include docs *.md
recursive-include docs *.txt
recursive-include docs Makefile

# Exclude development and cache files
global-exclude *.pyc
global-exclude *.pyo
global-exclude *.pyd
global-exclude __pycache__
global-exclude .git*
global-exclude .DS_Store
global-exclude *.so
global-exclude .coverage
global-exclude .pytest_cache
global-exclude .mypy_cache
global-exclude .tox
global-exclude .venv
global-exclude venv
global-exclude env
global-exclude node_modules
global-exclude .next
global-exclude dist
global-exclude build
global-exclude *.egg-info
global-exclude *.pid
global-exclude *.db
global-exclude *.sqlite
global-exclude *.sqlite3
global-exclude *.log
global-exclude scrapy_projects
