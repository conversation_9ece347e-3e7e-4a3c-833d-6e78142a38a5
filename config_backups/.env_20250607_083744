# ScrapyUI Environment Configuration
# データベース設定: MYSQL

SCRAPY_UI_ENV=production
DEBUG=false
SECRET_KEY=scrapyui-secret-key-1749206289

# MySQL設定
DATABASE_TYPE=mysql
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_NAME=scrapy_ui
DATABASE_USER=scrapy_user
DATABASE_PASSWORD=ScrapyUser%402024%23
DATABASE_CHARSET=utf8mb4
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20
DATABASE_ECHO=false

# JWT設定
JWT_SECRET_KEY=scrapyui-jwt-secret-1749206297
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# Scrapy設定
SCRAPY_PROJECTS_DIR=./scrapy_projects
SCRAPY_LOGS_DIR=./logs
SCRAPY_RESULTS_DIR=./results

# セキュリティ設定
CORS_ORIGINS=http://localhost:4000,http://localhost:3001,http://localhost:3002
ALLOWED_HOSTS=localhost,127.0.0.1

# ログ設定
LOG_LEVEL=INFO
LOG_FILE=scrapy_ui.log

# タイムゾーン設定
TIMEZONE=Asia/Tokyo
TZ=Asia/Tokyo

# Node.js Service Configuration
NODEJS_SERVICE_URL=http://localhost:3001
NODEJS_SERVICE_API_KEY=scrapyui-nodejs-service-key-2024

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Flower Configuration
AUTO_START_FLOWER=true
FLOWER_MODE=api
FLOWER_PORT=5556
FLOWER_HOST=127.0.0.1
