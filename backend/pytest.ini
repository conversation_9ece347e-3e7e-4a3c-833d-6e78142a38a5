[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = -v --tb=short
markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    slow: Slow running tests
    auth: Authentication tests
    api: API tests
    database: Database tests
    websocket: WebSocket tests
    ai: AI service tests
    export: Export functionality tests
    performance: Performance tests
    security: Security tests
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
