# ScrapyUI MySQL設定例
# このファイルを .env にコピーして使用してください

# アプリケーション設定
SCRAPY_UI_ENV=production
DEBUG=false
SECRET_KEY=your-secret-key-here

# MySQL データベース設定
DATABASE_TYPE=mysql
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_NAME=scrapy_ui
DATABASE_USER=scrapy_user
DATABASE_PASSWORD=your_secure_password
DATABASE_CHARSET=utf8mb4
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20
DATABASE_ECHO=false

# JWT設定
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# Scrapy設定
SCRAPY_PROJECTS_DIR=./scrapy_projects
SCRAPY_LOGS_DIR=./logs
SCRAPY_RESULTS_DIR=./results

# セキュリティ設定
CORS_ORIGINS=http://localhost:4000,http://localhost:3001,http://localhost:3002
ALLOWED_HOSTS=localhost,127.0.0.1

# ログ設定
LOG_LEVEL=INFO
LOG_FILE=scrapy_ui.log
