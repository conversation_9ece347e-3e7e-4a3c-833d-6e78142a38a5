#!/usr/bin/env python3
"""
Celery Beat起動スクリプト
データベースベースのスケジューリング
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# ログ設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('celery_beat.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def start_celery_beat():
    """Celery Beatを起動"""
    try:
        logger.info("🚀 Celery Beat起動中...")
        
        # Celery Beatコマンド
        cmd = [
            sys.executable, '-m', 'celery',
            '-A', 'app.celery_app',
            'beat',
            '--loglevel=info',
            '--scheduler=django_celery_beat.schedulers:DatabaseScheduler',
            '--max-interval=60',  # 最大60秒間隔でスケジュールチェック
        ]
        
        logger.info(f"📋 実行コマンド: {' '.join(cmd)}")
        
        # プロセス起動
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        logger.info(f"✅ Celery Beat起動: PID {process.pid}")
        
        # 出力をリアルタイムで表示
        try:
            for line in iter(process.stdout.readline, ''):
                if line:
                    print(line.rstrip())
        except KeyboardInterrupt:
            logger.info("🛑 Celery Beat停止中...")
            process.terminate()
            process.wait()
            
    except Exception as e:
        logger.error(f"❌ Celery Beat起動エラー: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    start_celery_beat()
