#!/usr/bin/env python3
"""
Scrapyプロセス実行ラッパー
根本的な安定性向上とエラー処理
"""

import os
import sys
import subprocess
import signal
import time
import logging
import json
from pathlib import Path
from datetime import datetime
from typing import Optional, Dict, Any

# ログ設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ScrapyProcessWrapper:
    """Scrapyプロセスの安定実行ラッパー"""
    
    def __init__(self, task_id: str, project_path: str, spider_name: str, output_file: str):
        self.task_id = task_id
        self.project_path = project_path
        self.spider_name = spider_name
        self.output_file = output_file
        self.process: Optional[subprocess.Popen] = None
        self.start_time = None
        self.end_time = None
        self.success = False
        self.error_message = None
        
    def setup_signal_handlers(self):
        """シグナルハンドラーの設定"""
        def signal_handler(signum, frame):
            logger.warning(f"🛑 シグナル受信: {signum}")
            self.terminate_gracefully()
            
        signal.signal(signal.SIGTERM, signal_handler)
        signal.signal(signal.SIGINT, signal_handler)
    
    def validate_environment(self) -> bool:
        """実行環境の検証"""
        try:
            # プロジェクトディレクトリの存在確認
            if not os.path.exists(self.project_path):
                self.error_message = f"プロジェクトディレクトリが存在しません: {self.project_path}"
                return False
            
            # scrapy.cfgの存在確認
            scrapy_cfg = os.path.join(self.project_path, 'scrapy.cfg')
            if not os.path.exists(scrapy_cfg):
                self.error_message = f"scrapy.cfgが見つかりません: {scrapy_cfg}"
                return False
            
            # 出力ディレクトリの作成
            output_dir = os.path.dirname(self.output_file)
            os.makedirs(output_dir, exist_ok=True)
            
            # ディスク容量チェック
            stat = os.statvfs(output_dir)
            free_space_gb = (stat.f_bavail * stat.f_frsize) / (1024**3)
            if free_space_gb < 1.0:  # 1GB未満
                self.error_message = f"ディスク容量不足: {free_space_gb:.2f}GB"
                return False
            
            logger.info(f"✅ 実行環境検証完了")
            return True
            
        except Exception as e:
            self.error_message = f"環境検証エラー: {str(e)}"
            return False
    
    def build_command(self) -> list:
        """Scrapyコマンドの構築"""
        cmd = [
            sys.executable, '-m', 'scrapy',
            'crawlwithwatchdog',
            self.spider_name,
            '-o', self.output_file,
            f'--task-id={self.task_id}',
            '-s', 'FEED_FORMAT=jsonlines',
            '-s', 'LOG_LEVEL=INFO',
            '-s', 'DOWNLOAD_DELAY=3',
            '-s', 'CONCURRENT_REQUESTS=1',
            '-s', 'RETRY_TIMES=3',
            '-s', 'RETRY_HTTP_CODES=[500,502,503,504,408,429]',
            '-s', 'DOWNLOAD_TIMEOUT=30',
            '-s', 'RANDOMIZE_DOWNLOAD_DELAY=0.5',
            # 安定性向上設定
            '-s', 'AUTOTHROTTLE_ENABLED=True',
            '-s', 'AUTOTHROTTLE_START_DELAY=1',
            '-s', 'AUTOTHROTTLE_MAX_DELAY=10',
            '-s', 'AUTOTHROTTLE_TARGET_CONCURRENCY=1.0',
            '-s', 'HTTPCACHE_ENABLED=True',
            '-s', 'HTTPCACHE_EXPIRATION_SECS=3600',
            # メモリ制限
            '-s', 'MEMUSAGE_ENABLED=True',
            '-s', 'MEMUSAGE_LIMIT_MB=500',
            '-s', 'MEMUSAGE_WARNING_MB=400',
        ]
        
        return cmd
    
    def execute_with_monitoring(self) -> Dict[str, Any]:
        """監視付きでScrapyを実行"""
        try:
            # 環境検証
            if not self.validate_environment():
                return {
                    'success': False,
                    'error': self.error_message,
                    'items_count': 0,
                    'duration': 0
                }
            
            # シグナルハンドラー設定
            self.setup_signal_handlers()
            
            # コマンド構築
            cmd = self.build_command()
            logger.info(f"🚀 Scrapyプロセス開始: {' '.join(cmd)}")
            
            # プロセス開始
            self.start_time = datetime.now()
            self.process = subprocess.Popen(
                cmd,
                cwd=self.project_path,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1,
                preexec_fn=os.setsid  # プロセスグループ作成
            )
            
            logger.info(f"✅ Scrapyプロセス開始: PID {self.process.pid}")
            
            # プロセス監視
            return self.monitor_process()
            
        except Exception as e:
            self.error_message = f"実行エラー: {str(e)}"
            logger.error(f"❌ {self.error_message}")
            return {
                'success': False,
                'error': self.error_message,
                'items_count': 0,
                'duration': 0
            }
    
    def monitor_process(self) -> Dict[str, Any]:
        """プロセスの監視"""
        try:
            # プロセス完了を待機（タイムアウト30分）
            timeout = 30 * 60  # 30分
            
            try:
                stdout, stderr = self.process.communicate(timeout=timeout)
                exit_code = self.process.returncode
                
            except subprocess.TimeoutExpired:
                logger.warning("⏰ Scrapyプロセスがタイムアウトしました")
                self.terminate_gracefully()
                exit_code = -1
                stdout = ""
                stderr = "Process timeout"
            
            self.end_time = datetime.now()
            duration = (self.end_time - self.start_time).total_seconds()
            
            # 結果ファイルの確認
            items_count = self.count_output_items()
            
            # 成功判定
            if exit_code == 0 and items_count > 0:
                self.success = True
                logger.info(f"✅ Scrapyプロセス正常完了: {items_count}アイテム, {duration:.1f}秒")
            elif exit_code == 0 and items_count == 0:
                logger.warning(f"⚠️ Scrapyプロセス完了（データなし）: {duration:.1f}秒")
                self.success = True  # データなしでも正常終了扱い
            else:
                logger.error(f"❌ Scrapyプロセス失敗: 終了コード {exit_code}, {duration:.1f}秒")
                self.error_message = f"Process failed with exit code {exit_code}"
            
            return {
                'success': self.success,
                'error': self.error_message,
                'items_count': items_count,
                'duration': duration,
                'exit_code': exit_code,
                'stdout': stdout[:1000] if stdout else "",  # 最初の1000文字のみ
            }
            
        except Exception as e:
            self.error_message = f"監視エラー: {str(e)}"
            logger.error(f"❌ {self.error_message}")
            return {
                'success': False,
                'error': self.error_message,
                'items_count': 0,
                'duration': 0
            }
    
    def count_output_items(self) -> int:
        """出力ファイルのアイテム数をカウント"""
        try:
            if not os.path.exists(self.output_file):
                return 0
            
            with open(self.output_file, 'r', encoding='utf-8') as f:
                count = sum(1 for line in f if line.strip())
            
            logger.info(f"📊 出力ファイル: {count}行")
            return count
            
        except Exception as e:
            logger.error(f"❌ ファイル読み取りエラー: {str(e)}")
            return 0
    
    def terminate_gracefully(self):
        """プロセスの正常終了"""
        if self.process and self.process.poll() is None:
            try:
                logger.info("🛑 Scrapyプロセスを正常終了中...")
                
                # プロセスグループ全体に SIGTERM 送信
                os.killpg(os.getpgid(self.process.pid), signal.SIGTERM)
                
                # 10秒待機
                try:
                    self.process.wait(timeout=10)
                    logger.info("✅ Scrapyプロセスが正常終了しました")
                except subprocess.TimeoutExpired:
                    # 強制終了
                    logger.warning("⚠️ 正常終了がタイムアウト。強制終了します")
                    os.killpg(os.getpgid(self.process.pid), signal.SIGKILL)
                    self.process.wait()
                    
            except Exception as e:
                logger.error(f"❌ プロセス終了エラー: {str(e)}")

def run_scrapy_with_wrapper(task_id: str, project_path: str, spider_name: str, output_file: str) -> Dict[str, Any]:
    """ラッパー経由でScrapyを実行"""
    wrapper = ScrapyProcessWrapper(task_id, project_path, spider_name, output_file)
    return wrapper.execute_with_monitoring()

if __name__ == "__main__":
    # テスト実行
    if len(sys.argv) != 5:
        print("Usage: python scrapy_process_wrapper.py <task_id> <project_path> <spider_name> <output_file>")
        sys.exit(1)
    
    task_id, project_path, spider_name, output_file = sys.argv[1:5]
    result = run_scrapy_with_wrapper(task_id, project_path, spider_name, output_file)
    
    print(json.dumps(result, indent=2))
    sys.exit(0 if result['success'] else 1)
