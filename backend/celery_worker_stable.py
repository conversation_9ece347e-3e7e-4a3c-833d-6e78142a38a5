#!/usr/bin/env python3
"""
安定性向上版Celeryワーカー起動スクリプト
SIGTERM対策とプロセス監視機能付き
"""

import os
import sys
import signal
import time
import subprocess
import logging
from pathlib import Path

# ログ設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('celery_worker_stable.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class StableCeleryWorker:
    """安定性向上版Celeryワーカー"""
    
    def __init__(self):
        self.worker_process = None
        self.should_restart = True
        self.restart_count = 0
        self.max_restarts = 10
        self.restart_delay = 5
        
    def signal_handler(self, signum, frame):
        """シグナルハンドラー"""
        logger.info(f'🛑 シグナル受信: {signum}')
        self.should_restart = False
        
        if self.worker_process:
            logger.info('📋 Celeryワーカーを正常終了中...')
            try:
                # 正常終了を試行
                self.worker_process.terminate()
                self.worker_process.wait(timeout=30)
                logger.info('✅ Celeryワーカーが正常終了しました')
            except subprocess.TimeoutExpired:
                logger.warning('⚠️ 正常終了がタイムアウト。強制終了します')
                self.worker_process.kill()
                self.worker_process.wait()
            except Exception as e:
                logger.error(f'❌ 終了処理エラー: {str(e)}')
        
        sys.exit(0)
    
    def start_worker(self):
        """Celeryワーカーを起動"""
        try:
            # Celeryワーカーコマンド（安定性向上設定）
            cmd = [
                sys.executable, '-m', 'celery',
                '-A', 'app.celery_app',
                'worker',
                '--loglevel=info',
                '--concurrency=2',
                '--queues=scrapy,monitoring,maintenance',
                '--pool=prefork',
                '--max-memory-per-child=200000',  # 200MB
                '--max-tasks-per-child=50',      # タスク数制限を厳しく
                '--time-limit=1800',             # 30分タイムアウト
                '--soft-time-limit=1500',        # 25分ソフトタイムアウト
                '--without-gossip',              # ゴシップ無効化
                '--without-mingle',              # ミングル無効化
                '--without-heartbeat',           # ハートビート無効化
                '--optimization=fair',           # 公平スケジューリング
            ]
            
            logger.info(f'🚀 Celeryワーカーを起動中... (試行 {self.restart_count + 1})')
            logger.info(f'📋 コマンド: {" ".join(cmd)}')
            
            # プロセス起動
            self.worker_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            logger.info(f'✅ Celeryワーカー起動: PID {self.worker_process.pid}')
            return True
            
        except Exception as e:
            logger.error(f'❌ Celeryワーカー起動エラー: {str(e)}')
            return False
    
    def monitor_worker(self):
        """ワーカーを監視"""
        while self.should_restart:
            if not self.worker_process or self.worker_process.poll() is not None:
                # プロセスが終了している
                if self.worker_process:
                    exit_code = self.worker_process.returncode
                    logger.warning(f'⚠️ Celeryワーカーが終了しました (終了コード: {exit_code})')
                
                if self.restart_count >= self.max_restarts:
                    logger.error(f'❌ 最大再起動回数 ({self.max_restarts}) に達しました')
                    break
                
                if self.should_restart:
                    logger.info(f'🔄 {self.restart_delay}秒後に再起動します...')
                    time.sleep(self.restart_delay)
                    
                    if self.start_worker():
                        self.restart_count += 1
                        # 再起動間隔を徐々に延長
                        self.restart_delay = min(self.restart_delay * 1.5, 60)
                    else:
                        logger.error('❌ 再起動に失敗しました')
                        break
            else:
                # プロセスが正常動作中
                time.sleep(10)  # 10秒間隔で監視
        
        logger.info('🛑 ワーカー監視を終了します')
    
    def run(self):
        """メイン実行"""
        # シグナルハンドラー設定
        signal.signal(signal.SIGTERM, self.signal_handler)
        signal.signal(signal.SIGINT, self.signal_handler)
        
        logger.info('🚀 安定性向上版Celeryワーカーを開始します')
        
        # 初回起動
        if self.start_worker():
            self.restart_count = 1
            # 監視ループ
            self.monitor_worker()
        else:
            logger.error('❌ 初回起動に失敗しました')
            sys.exit(1)

if __name__ == "__main__":
    worker = StableCeleryWorker()
    worker.run()
