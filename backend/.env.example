# ScrapyUI Environment Configuration
# 環境設定ファイル（.envにコピーして使用）

# アプリケーション設定
SCRAPY_UI_ENV=development
DEBUG=true
SECRET_KEY=your-secret-key-here

# データベース設定（環境変数での上書き）
# SQLite（デフォルト）- backend/database/ディレクトリ内
DATABASE_TYPE=sqlite
DATABASE_NAME=backend/database/scrapy_ui.db
DATABASE_ECHO=false

# MySQL設定例
# DATABASE_TYPE=mysql
# DATABASE_HOST=localhost
# DATABASE_PORT=3306
# DATABASE_NAME=scrapy_ui
# DATABASE_USER=scrapy_user
# DATABASE_PASSWORD=your_password
# DATABASE_CHARSET=utf8mb4
# DATABASE_POOL_SIZE=10
# DATABASE_MAX_OVERFLOW=20

# PostgreSQL設定例
# DATABASE_TYPE=postgresql
# DATABASE_HOST=localhost
# DATABASE_PORT=5432
# DATABASE_NAME=scrapy_ui
# DATABASE_USER=scrapy_user
# DATABASE_PASSWORD=your_password
# DATABASE_POOL_SIZE=10
# DATABASE_MAX_OVERFLOW=20

# MongoDB設定例（ドキュメントストレージ用）
# MONGODB_HOST=localhost
# MONGODB_PORT=27017
# MONGODB_DATABASE=scrapy_ui_docs
# MONGODB_USER=scrapy_user
# MONGODB_PASSWORD=your_password

# Elasticsearch設定例（検索・ログ用）
# ELASTICSEARCH_HOSTS=http://localhost:9200
# ELASTICSEARCH_INDEX_PREFIX=scrapy_ui
# ELASTICSEARCH_USERNAME=elastic
# ELASTICSEARCH_PASSWORD=your_password

# Redis設定例（キャッシュ・セッション用）
# REDIS_HOST=localhost
# REDIS_PORT=6379
# REDIS_DB=0
# REDIS_PASSWORD=your_password

# Celery設定
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Flower設定
FLOWER_PORT=5556
FLOWER_HOST=127.0.0.1
AUTO_START_FLOWER=true
FLOWER_MODE=all

# JWT設定
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# Scrapy設定
SCRAPY_PROJECTS_DIR=./scrapy_projects
SCRAPY_LOGS_DIR=./logs
SCRAPY_RESULTS_DIR=./results

# ファイルアップロード設定
MAX_UPLOAD_SIZE=10485760  # 10MB
ALLOWED_EXTENSIONS=py,txt,json,csv,xml

# セキュリティ設定
CORS_ORIGINS=http://localhost:4000,http://localhost:3001,http://localhost:3002
ALLOWED_HOSTS=localhost,127.0.0.1

# ログ設定
LOG_LEVEL=INFO
LOG_FILE=scrapy_ui.log
LOG_MAX_SIZE=10485760  # 10MB
LOG_BACKUP_COUNT=5

# 外部サービス設定
# Playwright設定
PLAYWRIGHT_BROWSER_TYPE=chromium
PLAYWRIGHT_HEADLESS=true
PLAYWRIGHT_TIMEOUT=30000

# プロキシ設定
PROXY_ENABLED=false
PROXY_ROTATION=false

# 通知設定
NOTIFICATIONS_ENABLED=true
EMAIL_NOTIFICATIONS=false
WEBHOOK_NOTIFICATIONS=false

# パフォーマンス設定
WORKER_PROCESSES=1
WORKER_CONNECTIONS=1000
KEEPALIVE_TIMEOUT=5

# 開発設定
RELOAD=true
ACCESS_LOG=true
ERROR_LOG=true
