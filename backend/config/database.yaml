default:
  type: "mysql"
  host: "localhost"
  port: 3306
  database: "scrapy_ui"
  username: "scrapy_user"
  password: "ScrapyUser%402024%23"
  charset: "utf8mb4"
  echo: false
  pool_size: 15
  max_overflow: 30
development:
  database: backend/database/scrapy_ui.db
  echo: false
  type: sqlite
elasticsearch_example:
  hosts:
  - http://localhost:9200
  index_prefix: scrapy_ui
  options:
    http_auth:
      password: your_password
      username: elastic
    max_retries: 3
    retry_on_timeout: true
    timeout: 30
    use_ssl: false
    verify_certs: false
  type: elasticsearch
mongodb_example:
  database: scrapy_ui
  host: localhost
  options:
    authSource: admin
    retryWrites: true
    w: majority
  password: your_password
  port: 27017
  type: mongodb
  username: scrapy_user
multi_database_example:
  cache:
    database: 0
    host: localhost
    port: 6379
    type: redis
  document:
    database: scrapy_ui_docs
    host: localhost
    password: your_password
    port: 27017
    type: mongodb
    username: scrapy_user
  primary:
    database: scrapy_ui_main
    host: localhost
    password: your_password
    port: 5432
    type: postgresql
    username: scrapy_user
  search:
    hosts:
    - http://localhost:9200
    index_prefix: scrapy_ui
    type: elasticsearch
mysql_example:
  charset: utf8mb4
  database: scrapy_ui
  echo: false
  host: localhost
  max_overflow: 20
  password: your_password
  pool_size: 10
  port: 3306
  type: mysql
  username: scrapy_user
mysql_production:
  charset: utf8mb4
  database: scrapy_ui
  echo: false
  host: localhost
  max_overflow: 30
  password: ScrapyUser%402024%23
  pool_size: 15
  port: 3306
  type: mysql
  username: scrapy_user
postgresql_example:
  database: scrapy_ui
  echo: false
  host: localhost
  max_overflow: 20
  options:
    connect_timeout: 10
    sslmode: prefer
  password: your_password
  pool_size: 10
  port: 5432
  type: postgresql
  username: scrapy_user
production:
  database: backend/database/scrapy_ui.db
  echo: false
  type: sqlite
production_postgresql:
  database: scrapy_ui_prod
  echo: false
  host: localhost
  max_overflow: 20
  password: your_password
  pool_size: 10
  port: 5432
  type: postgresql
  username: scrapy_user
redis_example:
  database: 0
  host: localhost
  options:
    decode_responses: true
    max_connections: 10
    retry_on_timeout: true
    socket_connect_timeout: 5
    socket_timeout: 5
  password: your_password
  port: 6379
  type: redis
testing:
  database: backend/database/scrapy_ui.db
  echo: false
  type: sqlite
usedatabase: default
