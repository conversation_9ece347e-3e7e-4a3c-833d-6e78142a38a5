#!/usr/bin/env python3
"""
プロセス監視・自動復旧サービス
Celeryワーカーとスケジューラーの安定性を保証
"""

import os
import sys
import time
import psutil
import subprocess
import logging
from datetime import datetime, timedelta
from pathlib import Path

# ログ設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('process_monitor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ProcessMonitor:
    """プロセス監視・自動復旧システム"""
    
    def __init__(self):
        self.check_interval = 30  # 30秒間隔でチェック
        self.restart_threshold = 3  # 3回連続失敗で警告
        self.failure_counts = {}
        self.last_restart_times = {}
        self.min_restart_interval = 60  # 最小再起動間隔（秒）
        
    def check_celery_worker(self):
        """Celeryワーカーの状態チェック"""
        try:
            worker_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'status']):
                cmdline = ' '.join(proc.info['cmdline'] or [])
                if 'celery' in cmdline and 'worker' in cmdline:
                    worker_processes.append({
                        'pid': proc.info['pid'],
                        'status': proc.info['status'],
                        'cmdline': cmdline
                    })
            
            if len(worker_processes) >= 1:
                logger.info(f"✅ Celeryワーカー正常: {len(worker_processes)}プロセス")
                self.failure_counts['celery_worker'] = 0
                return True
            else:
                logger.warning("⚠️ Celeryワーカーが見つかりません")
                return False
                
        except Exception as e:
            logger.error(f"❌ Celeryワーカーチェックエラー: {str(e)}")
            return False
    
    def check_scheduler(self):
        """統一スケジューラーの状態チェック"""
        try:
            scheduler_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'status']):
                cmdline = ' '.join(proc.info['cmdline'] or [])
                if 'scheduler_service' in cmdline or 'start_unified_scheduler' in cmdline:
                    scheduler_processes.append({
                        'pid': proc.info['pid'],
                        'status': proc.info['status'],
                        'cmdline': cmdline
                    })
            
            if len(scheduler_processes) >= 1:
                logger.info(f"✅ 統一スケジューラー正常: {len(scheduler_processes)}プロセス")
                self.failure_counts['scheduler'] = 0
                return True
            else:
                logger.warning("⚠️ 統一スケジューラーが見つかりません")
                return False
                
        except Exception as e:
            logger.error(f"❌ スケジューラーチェックエラー: {str(e)}")
            return False
    
    def restart_celery_worker(self):
        """Celeryワーカーを再起動"""
        try:
            # 最小再起動間隔のチェック
            last_restart = self.last_restart_times.get('celery_worker')
            if last_restart:
                time_since_restart = (datetime.now() - last_restart).total_seconds()
                if time_since_restart < self.min_restart_interval:
                    logger.info(f"⏳ Celeryワーカー再起動間隔不足: {time_since_restart:.0f}秒")
                    return False
            
            logger.info("🔄 Celeryワーカーを再起動中...")
            
            # 既存プロセスを停止
            subprocess.run(['pkill', '-f', 'celery.*worker'], check=False)
            time.sleep(5)
            
            # 安定版ワーカーを起動
            worker_script = Path(__file__).parent / 'celery_worker_stable.py'
            if worker_script.exists():
                subprocess.Popen([
                    sys.executable, str(worker_script)
                ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                
                self.last_restart_times['celery_worker'] = datetime.now()
                logger.info("✅ Celeryワーカーを再起動しました")
                return True
            else:
                logger.error("❌ 安定版ワーカースクリプトが見つかりません")
                return False
                
        except Exception as e:
            logger.error(f"❌ Celeryワーカー再起動エラー: {str(e)}")
            return False
    
    def restart_scheduler(self):
        """統一スケジューラーを再起動"""
        try:
            # 最小再起動間隔のチェック
            last_restart = self.last_restart_times.get('scheduler')
            if last_restart:
                time_since_restart = (datetime.now() - last_restart).total_seconds()
                if time_since_restart < self.min_restart_interval:
                    logger.info(f"⏳ スケジューラー再起動間隔不足: {time_since_restart:.0f}秒")
                    return False
            
            logger.info("🔄 統一スケジューラーを再起動中...")
            
            # 既存プロセスを停止
            subprocess.run(['pkill', '-f', 'scheduler_service'], check=False)
            time.sleep(3)
            
            # スケジューラーを起動
            scheduler_script = Path(__file__).parent / 'start_unified_scheduler.py'
            if scheduler_script.exists():
                subprocess.Popen([
                    sys.executable, str(scheduler_script)
                ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                
                self.last_restart_times['scheduler'] = datetime.now()
                logger.info("✅ 統一スケジューラーを再起動しました")
                return True
            else:
                logger.error("❌ スケジューラー起動スクリプトが見つかりません")
                return False
                
        except Exception as e:
            logger.error(f"❌ スケジューラー再起動エラー: {str(e)}")
            return False
    
    def run_monitoring(self):
        """監視ループを実行"""
        logger.info("🚀 プロセス監視を開始します")
        
        while True:
            try:
                # Celeryワーカーのチェック
                if not self.check_celery_worker():
                    self.failure_counts['celery_worker'] = self.failure_counts.get('celery_worker', 0) + 1
                    
                    if self.failure_counts['celery_worker'] >= self.restart_threshold:
                        logger.error(f"❌ Celeryワーカー連続失敗: {self.failure_counts['celery_worker']}回")
                        if self.restart_celery_worker():
                            self.failure_counts['celery_worker'] = 0
                
                # 統一スケジューラーのチェック
                if not self.check_scheduler():
                    self.failure_counts['scheduler'] = self.failure_counts.get('scheduler', 0) + 1
                    
                    if self.failure_counts['scheduler'] >= self.restart_threshold:
                        logger.error(f"❌ スケジューラー連続失敗: {self.failure_counts['scheduler']}回")
                        if self.restart_scheduler():
                            self.failure_counts['scheduler'] = 0
                
                # 次のチェックまで待機
                time.sleep(self.check_interval)
                
            except KeyboardInterrupt:
                logger.info("🛑 監視を停止します")
                break
            except Exception as e:
                logger.error(f"❌ 監視ループエラー: {str(e)}")
                time.sleep(self.check_interval)

if __name__ == "__main__":
    monitor = ProcessMonitor()
    monitor.run_monitoring()
