#!/usr/bin/env python3
"""
Celery Beat動的スケジューラー
データベースからスケジュールを読み込んでCelery Beatで実行
"""

import os
import sys
import logging
from datetime import datetime, timedelta
from typing import Dict, Any
import pytz

# Django設定（Celery Beat用）
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'celery_beat_settings')

from celery import Celery
from celery.schedules import crontab
from kombu import Queue

# ログ設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('celery_beat.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DatabaseScheduler:
    """データベースベースのスケジューラー"""
    
    def __init__(self):
        self.timezone = pytz.timezone('Asia/Tokyo')
        
    def get_database_schedules(self) -> Dict[str, Any]:
        """データベースからスケジュールを取得"""
        try:
            # 直接データベースアクセス
            import mysql.connector
            
            conn = mysql.connector.connect(
                host='localhost',
                user='scrapy_user',
                password='ScrapyUser@2024#',
                database='scrapy_ui',
                charset='utf8mb4'
            )
            
            cursor = conn.cursor(dictionary=True)
            cursor.execute("""
                SELECT id, name, spider_name, project_name, cron_expression, 
                       is_active, next_run, last_run
                FROM schedules 
                WHERE is_active = 1
            """)
            
            schedules = cursor.fetchall()
            cursor.close()
            conn.close()
            
            logger.info(f"📊 データベースから{len(schedules)}個のアクティブスケジュールを取得")
            return schedules
            
        except Exception as e:
            logger.error(f"❌ データベースアクセスエラー: {str(e)}")
            return []
    
    def parse_cron_expression(self, cron_expr: str) -> Dict[str, Any]:
        """Cron式をCeleryのcrontab形式に変換"""
        try:
            # "0 16 * * *" -> minute=0, hour=16, day_of_month=*, month_of_year=*, day_of_week=*
            parts = cron_expr.strip().split()
            if len(parts) != 5:
                raise ValueError(f"Invalid cron expression: {cron_expr}")
            
            minute, hour, day, month, day_of_week = parts
            
            # Celery crontab形式に変換
            cron_kwargs = {}
            
            if minute != '*':
                cron_kwargs['minute'] = int(minute)
            if hour != '*':
                cron_kwargs['hour'] = int(hour)
            if day != '*':
                cron_kwargs['day_of_month'] = int(day)
            if month != '*':
                cron_kwargs['month_of_year'] = int(month)
            if day_of_week != '*':
                cron_kwargs['day_of_week'] = int(day_of_week)
            
            return cron_kwargs
            
        except Exception as e:
            logger.error(f"❌ Cron式解析エラー: {cron_expr} - {str(e)}")
            return {'minute': 0, 'hour': 0}  # デフォルト: 毎日0:00
    
    def generate_beat_schedule(self) -> Dict[str, Any]:
        """Celery Beat用のスケジュール設定を生成"""
        schedules = self.get_database_schedules()
        beat_schedule = {}
        
        for schedule in schedules:
            try:
                schedule_id = schedule['id']
                schedule_name = schedule['name']
                cron_expr = schedule['cron_expression']
                
                # Cron式を解析
                cron_kwargs = self.parse_cron_expression(cron_expr)
                
                # Celery Beatタスク設定
                task_name = f"schedule_{schedule_name}_{schedule_id[:8]}"
                beat_schedule[task_name] = {
                    'task': 'app.tasks.scrapy_tasks.scheduled_spider_run',
                    'schedule': crontab(**cron_kwargs),
                    'args': (
                        schedule_id,
                        schedule['project_name'],
                        schedule['spider_name']
                    ),
                    'options': {
                        'queue': 'scrapy',
                        'routing_key': 'scrapy',
                    }
                }
                
                logger.info(f"✅ スケジュール追加: {schedule_name} ({cron_expr})")
                
            except Exception as e:
                logger.error(f"❌ スケジュール設定エラー: {schedule['name']} - {str(e)}")
        
        logger.info(f"📊 Celery Beat設定完了: {len(beat_schedule)}個のスケジュール")
        return beat_schedule

# Celeryアプリケーション設定
def create_celery_beat_app():
    """Celery Beatアプリケーションを作成"""
    
    # スケジューラーインスタンス
    scheduler = DatabaseScheduler()
    
    # 動的スケジュール生成
    beat_schedule = scheduler.generate_beat_schedule()
    
    # Celeryアプリ作成
    app = Celery('scrapyui_beat')
    
    # 設定
    app.conf.update(
        # ブローカー設定
        broker_url='redis://localhost:6379/0',
        result_backend='redis://localhost:6379/0',
        
        # タイムゾーン設定
        timezone='Asia/Tokyo',
        enable_utc=False,
        
        # Beat設定
        beat_schedule=beat_schedule,
        beat_scheduler='django_celery_beat.schedulers:DatabaseScheduler',
        
        # タスク設定
        task_serializer='json',
        accept_content=['json'],
        result_serializer='json',
        
        # ワーカー設定
        task_routes={
            'app.tasks.scrapy_tasks.*': {'queue': 'scrapy'},
        },
        
        # その他設定
        worker_prefetch_multiplier=1,
        task_acks_late=True,
        worker_max_tasks_per_child=50,
    )
    
    # タスクの自動検出
    app.autodiscover_tasks(['app.tasks'])
    
    return app

# Celery Beatアプリケーション
celery_beat_app = create_celery_beat_app()

if __name__ == "__main__":
    # Celery Beat起動
    logger.info("🚀 Celery Beat スケジューラーを起動中...")
    
    # 引数処理
    if len(sys.argv) > 1 and sys.argv[1] == 'beat':
        # Beat起動
        celery_beat_app.start(['celery', 'beat', '--loglevel=info'])
    else:
        # スケジュール確認のみ
        scheduler = DatabaseScheduler()
        schedules = scheduler.get_database_schedules()
        
        print(f"📊 アクティブスケジュール: {len(schedules)}個")
        for schedule in schedules:
            print(f"   - {schedule['name']}: {schedule['cron_expression']}")
