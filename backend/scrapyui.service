[Unit]
Description=ScrapyUI Web Application
After=network.target redis.service
Wants=redis.service

[Service]
Type=simple
User=igtmtakan
Group=igtmtakan
WorkingDirectory=/home/<USER>/workplace/python/scrapyUI/backend
Environment=PATH=/home/<USER>/.pyenv/versions/3.13.2/bin:/usr/local/bin:/usr/bin:/bin
Environment=PYTHONPATH=/home/<USER>/workplace/python/scrapyUI/backend
ExecStart=/home/<USER>/.pyenv/versions/3.13.2/bin/python watchdog.py monitor
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=scrapyui

# セキュリティ設定
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/home/<USER>/workplace/python/scrapyUI

[Install]
WantedBy=multi-user.target
