#!/usr/bin/env python3
"""
データベースベースのCelery Beatスケジューラー
"""

import logging
from datetime import datetime, timedelta
from celery.beat import Scheduler, ScheduleEntry
from celery.schedules import crontab
import pytz

# ログ設定
logger = logging.getLogger(__name__)

class DatabaseScheduler(Scheduler):
    """データベースからスケジュールを読み込むCelery Beatスケジューラー"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.timezone = pytz.timezone('Asia/Tokyo')
        self._schedule = {}
        self._last_updated = None
        
    def setup_schedule(self):
        """スケジュールの初期設定"""
        logger.info("🔧 データベーススケジューラーを初期化中...")
        self.update_from_database()
        
    def update_from_database(self):
        """データベースからスケジュールを更新"""
        try:
            # データベース接続
            import mysql.connector
            
            conn = mysql.connector.connect(
                host='localhost',
                user='scrapy_user',
                password='ScrapyUser@2024#',
                database='scrapy_ui',
                charset='utf8mb4'
            )
            
            cursor = conn.cursor(dictionary=True)
            cursor.execute("""
                SELECT id, name, spider_name, project_name, cron_expression, 
                       is_active, next_run, last_run
                FROM schedules 
                WHERE is_active = 1
            """)
            
            schedules = cursor.fetchall()
            cursor.close()
            conn.close()
            
            # 既存のスケジュールをクリア
            new_schedule = {}
            
            # システムタスクを追加
            new_schedule.update({
                'cleanup-old-results': {
                    'task': 'app.tasks.scrapy_tasks.cleanup_old_results',
                    'schedule': crontab(minute=0, hour=2),
                },
                'system-health-check': {
                    'task': 'app.tasks.scrapy_tasks.system_health_check',
                    'schedule': crontab(minute='*/5'),
                },
                'auto-repair-failed-tasks': {
                    'task': 'app.tasks.scrapy_tasks.auto_repair_failed_tasks',
                    'schedule': crontab(minute='*/2'),
                },
                'cleanup-stuck-tasks': {
                    'task': 'app.tasks.scrapy_tasks.cleanup_stuck_tasks',
                    'schedule': crontab(minute='*/30'),
                }
            })
            
            # データベースのスケジュールを追加
            for schedule in schedules:
                try:
                    schedule_id = schedule['id']
                    schedule_name = schedule['name']
                    cron_expr = schedule['cron_expression']
                    
                    # Cron式を解析
                    cron_kwargs = self.parse_cron_expression(cron_expr)
                    
                    # スケジュールエントリを作成
                    task_name = f"schedule_{schedule_name}_{schedule_id[:8]}"
                    new_schedule[task_name] = {
                        'task': 'app.tasks.scrapy_tasks.scheduled_spider_run',
                        'schedule': crontab(**cron_kwargs),
                        'args': (schedule_id,),
                        'options': {
                            'queue': 'scrapy',
                        }
                    }
                    
                    logger.info(f"✅ スケジュール追加: {schedule_name} ({cron_expr})")
                    
                except Exception as e:
                    logger.error(f"❌ スケジュール設定エラー: {schedule['name']} - {str(e)}")
            
            # スケジュールを更新
            self._schedule = new_schedule
            self._last_updated = datetime.now()
            
            logger.info(f"📊 スケジュール更新完了: {len(schedules)}個のアクティブスケジュール")
            
        except Exception as e:
            logger.error(f"❌ データベースアクセスエラー: {str(e)}")
    
    def parse_cron_expression(self, cron_expr: str) -> dict:
        """Cron式をCeleryのcrontab形式に変換"""
        try:
            parts = cron_expr.strip().split()
            if len(parts) != 5:
                raise ValueError(f"Invalid cron expression: {cron_expr}")
            
            minute, hour, day, month, day_of_week = parts
            
            cron_kwargs = {}
            
            if minute != '*':
                if '/' in minute:
                    # */5 形式
                    interval = int(minute.split('/')[1])
                    cron_kwargs['minute'] = f'*/{interval}'
                else:
                    cron_kwargs['minute'] = int(minute)
            
            if hour != '*':
                if '/' in hour:
                    interval = int(hour.split('/')[1])
                    cron_kwargs['hour'] = f'*/{interval}'
                else:
                    cron_kwargs['hour'] = int(hour)
            
            if day != '*':
                cron_kwargs['day_of_month'] = int(day)
            if month != '*':
                cron_kwargs['month_of_year'] = int(month)
            if day_of_week != '*':
                cron_kwargs['day_of_week'] = int(day_of_week)
            
            return cron_kwargs
            
        except Exception as e:
            logger.error(f"❌ Cron式解析エラー: {cron_expr} - {str(e)}")
            return {'minute': 0, 'hour': 0}
    
    @property
    def schedule(self):
        """現在のスケジュールを返す"""
        # 5分ごとにデータベースから更新
        if (self._last_updated is None or 
            datetime.now() - self._last_updated > timedelta(minutes=5)):
            self.update_from_database()
        
        return self._schedule
    
    def tick(self):
        """スケジューラーのティック処理"""
        # 定期的にデータベースを更新
        if (self._last_updated is None or 
            datetime.now() - self._last_updated > timedelta(minutes=1)):
            self.update_from_database()
        
        return super().tick()
    
    def close(self):
        """スケジューラーのクリーンアップ"""
        logger.info("🛑 データベーススケジューラーを停止中...")
        super().close()
