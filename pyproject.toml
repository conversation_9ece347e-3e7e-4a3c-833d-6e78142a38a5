[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ScrapyUI"
dynamic = ["version"]
description = "Web-based Scrapy Management Interface with Node.js Puppeteer Integration"
readme = "README.md"
license = {file = "LICENSE"}
authors = [
    {name = "motoaki", email = "<EMAIL>"}
]
maintainers = [
    {name = "motoaki", email = "<EMAIL>"}
]
keywords = ["scrapy", "web-scraping", "ui", "interface", "monitoring", "puppeteer", "automation"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Topic :: Internet :: WWW/HTTP :: Dynamic Content",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: System :: Monitoring",
    "Topic :: Text Processing :: Markup :: HTML",
    "Framework :: FastAPI",
    "Framework :: Scrapy",
]
requires-python = ">=3.9"
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "sqlalchemy>=2.0.0",
    "alembic>=1.12.0",
    "pydantic>=2.5.0",
    "python-multipart>=0.0.6",
    "websockets>=12.0",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt,argon2]>=1.7.4",
    "argon2-cffi>=23.1.0",
    "bcrypt>=4.0.0,<4.1.0",
    "python-dotenv>=1.0.0",
    "aiofiles>=23.2.0",
    "celery>=5.3.0",
    "redis>=5.0.0",
    "PyYAML>=6.0.0",
    "pymysql>=1.1.0",
    "motor>=3.3.0",
    "elasticsearch>=8.11.0",
    "aioredis>=2.0.0",
    "psutil>=5.9.0",
    "scrapy>=2.12.0",
    "scrapy-playwright>=0.0.40",
    "playwright>=1.51.0",
    "croniter>=3.0.0",
    "pandas>=2.2.0",
    "openpyxl>=3.1.0",
    "beautifulsoup4>=4.12.0",
    "pyquery>=2.0.0",
    "openai>=1.54.0",
    "aiohttp>=3.11.0",
    "httpx>=0.28.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=8.3.0",
    "pytest-asyncio>=0.24.0",
    "pytest-mock>=3.14.0",
]
postgresql = [
    "psycopg2-binary>=2.9.9",
]
xml = [
    "lxml>=4.9.0",
]

[project.scripts]
scrapyui = "backend.app.cli:main"
scrapyui-server = "backend.app.main:start_server"
scrapyui-admin = "backend.create_admin:main"

[project.urls]
Homepage = "https://github.com/igtmtakan/scrapyUi"
Repository = "https://github.com/igtmtakan/scrapyUi"
Documentation = "https://github.com/igtmtakan/scrapyUi/blob/main/docs/"
"Bug Reports" = "https://github.com/igtmtakan/scrapyUi/issues"

[tool.setuptools]
packages = ["backend"]
include-package-data = true

[tool.setuptools.dynamic]
version = {file = "VERSION"}

[tool.setuptools.package-data]
backend = [
    "app/templates/*.json",
    "config/*.yaml",
    "scripts/*.py",
    "templates/*.json",
]

[tool.pytest.ini_options]
testpaths = ["backend/tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
asyncio_mode = "auto"

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | node_modules
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["backend"]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    "node_modules",
    ".venv",
]
